#!/usr/bin/env python3
"""
Database connection test utility
"""

import asyncio
import sys
import os

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_connection():
    """Test database connection."""
    try:
        from backend.core.database import engine, AsyncSession
        from backend.models.user import User
        from sqlalchemy import select, text
        
        print("🔗 Testing database connection...")
        
        # Test basic connection
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT 1"))
            print("✅ Basic database connection successful")
        
        # Test session and model
        async with AsyncSession(engine) as session:
            stmt = select(User).limit(1)
            result = await session.execute(stmt)
            user_count = len(result.all())
            print(f"✅ Database session working - Found {user_count} users")
        
        print("🎉 Database connection test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("💡 Make sure PostgreSQL is running and configured correctly")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    sys.exit(0 if success else 1)
