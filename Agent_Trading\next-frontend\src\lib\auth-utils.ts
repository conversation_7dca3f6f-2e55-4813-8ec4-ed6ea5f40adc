/**
 * Authentication Utilities
 * Provides robust token validation and authentication helpers
 */

interface TokenPayload {
  user_id: string
  email: string
  roles: string[]
  tier: string
  exp: number
  iat: number
  jti: string
}

/**
 * Decode JWT token without verification (for client-side validation)
 */
export function decodeJWT(token: string): TokenPayload | null {
  try {
    if (!token || typeof token !== 'string') {
      return null
    }

    const parts = token.split('.')
    if (parts.length !== 3) {
      return null
    }

    // Decode the payload (second part)
    const payload = parts[1]
    const decoded = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')))
    
    return decoded as TokenPayload
  } catch (error) {
    console.error('Failed to decode JWT:', error)
    return null
  }
}

/**
 * Check if JWT token is expired
 */
export function isTokenExpired(token: string): boolean {
  const payload = decodeJWT(token)
  if (!payload || !payload.exp) {
    return true
  }

  // Check if token expires within the next 30 seconds (buffer for network delay)
  const expirationTime = payload.exp * 1000 // Convert to milliseconds
  const currentTime = Date.now()
  const bufferTime = 30 * 1000 // 30 seconds buffer

  return currentTime >= (expirationTime - bufferTime)
}

/**
 * Validate JWT token format and structure
 */
export function isValidTokenFormat(token: string): boolean {
  if (!token || typeof token !== 'string') {
    return false
  }

  // Check basic JWT structure (3 parts separated by dots)
  const parts = token.split('.')
  if (parts.length !== 3) {
    return false
  }

  // Check if each part is base64 encoded
  try {
    for (const part of parts) {
      if (!part || part.length === 0) {
        return false
      }
      // Try to decode each part
      atob(part.replace(/-/g, '+').replace(/_/g, '/'))
    }
    return true
  } catch (error) {
    return false
  }
}

/**
 * Comprehensive token validation
 */
export function validateToken(token: string): {
  isValid: boolean
  reason?: string
  payload?: TokenPayload
} {
  // Check if token exists
  if (!token) {
    return { isValid: false, reason: 'Token is missing' }
  }

  // Check token format
  if (!isValidTokenFormat(token)) {
    return { isValid: false, reason: 'Invalid token format' }
  }

  // Decode token
  const payload = decodeJWT(token)
  if (!payload) {
    return { isValid: false, reason: 'Failed to decode token' }
  }

  // Check required fields
  if (!payload.user_id || !payload.email || !payload.exp) {
    return { isValid: false, reason: 'Token missing required fields' }
  }

  // Check if expired
  if (isTokenExpired(token)) {
    return { isValid: false, reason: 'Token has expired' }
  }

  return { isValid: true, payload }
}

/**
 * Get token from localStorage safely
 */
export function getStoredToken(): string | null {
  if (typeof window === 'undefined') {
    return null
  }

  try {
    return localStorage.getItem('access_token')
  } catch (error) {
    console.error('Failed to get token from localStorage:', error)
    return null
  }
}

/**
 * Clear authentication data from localStorage
 */
export function clearAuthData(): void {
  if (typeof window === 'undefined') {
    return
  }

  try {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_data')
  } catch (error) {
    console.error('Failed to clear auth data:', error)
  }
}

/**
 * Check if user is authenticated with valid token
 */
export function isAuthenticated(): boolean {
  const token = getStoredToken()
  if (!token) {
    return false
  }

  const validation = validateToken(token)
  return validation.isValid
}

/**
 * Get user info from token
 */
export function getUserFromToken(): TokenPayload | null {
  const token = getStoredToken()
  if (!token) {
    return null
  }

  const validation = validateToken(token)
  return validation.isValid ? validation.payload || null : null
}
