"""
Debug script to test async trading validation with real images.
"""

import base64
import os
import sys
import traceback
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from api.validators import AnalyzeRequestValidator, ImageValidator
from core.config import settings

def test_with_real_image():
    """Test validation with a real chart image."""
    
    # Look for test images in the Testing_images folder
    test_images_dir = Path("../Testing_images")
    if not test_images_dir.exists():
        test_images_dir = Path("../../Testing_images")
    if not test_images_dir.exists():
        test_images_dir = Path("C:/Agent_Trading/Agent_Trading/Testing_images")
    
    print(f"🔍 Looking for test images in: {test_images_dir.absolute()}")
    
    if not test_images_dir.exists():
        print("❌ Testing_images folder not found!")
        return
    
    # Find image files
    image_files = list(test_images_dir.glob("*.png")) + list(test_images_dir.glob("*.jpg")) + list(test_images_dir.glob("*.jpeg"))
    
    if not image_files:
        print("❌ No image files found in Testing_images folder!")
        return
    
    print(f"✅ Found {len(image_files)} image files")
    
    # Test with the first image
    test_image = image_files[0]
    print(f"🖼️ Testing with: {test_image.name}")
    
    try:
        # Read and encode image
        with open(test_image, 'rb') as f:
            image_bytes = f.read()
        
        # Convert to base64
        image_b64 = base64.b64encode(image_bytes).decode('utf-8')
        
        print(f"📏 Original image size: {len(image_bytes)} bytes")
        print(f"📏 Base64 encoded size: {len(image_b64)} characters")
        
        # Test ImageValidator directly
        print("\n🧪 Testing ImageValidator.validate_base64_image...")
        try:
            decoded_bytes = ImageValidator.validate_base64_image(image_b64)
            print(f"✅ ImageValidator passed! Decoded size: {len(decoded_bytes)} bytes")
        except Exception as e:
            print(f"❌ ImageValidator failed: {e}")
            return
        
        # Test AnalyzeRequestValidator
        print("\n🧪 Testing AnalyzeRequestValidator...")
        try:
            request = AnalyzeRequestValidator(
                images_base64=[image_b64],
                analysis_type='Positional',
                market_specialization='Crypto'
            )
            print("✅ AnalyzeRequestValidator passed!")
            print(f"📊 Request data: {len(request.images_base64)} images")
            
        except Exception as e:
            print(f"❌ AnalyzeRequestValidator failed: {e}")
            traceback.print_exc()
            return
            
        print("\n🎉 All validations passed! The issue is not with image validation.")
        
    except Exception as e:
        print(f"❌ Error reading test image: {e}")
        traceback.print_exc()

def test_with_minimal_valid_image():
    """Test with the smallest valid image (32x32)."""
    
    print("\n🧪 Testing with minimal valid image (32x32)...")
    
    try:
        from PIL import Image
        import io
        
        # Create a 32x32 white image
        img = Image.new('RGB', (32, 32), color='white')
        
        # Save to bytes
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes = img_bytes.getvalue()
        
        # Convert to base64
        img_b64 = base64.b64encode(img_bytes).decode('utf-8')
        
        print(f"📏 32x32 image size: {len(img_bytes)} bytes")
        
        # Test validation
        try:
            decoded_bytes = ImageValidator.validate_base64_image(img_b64)
            print(f"✅ 32x32 image validation passed!")
            
            # Test full request
            request = AnalyzeRequestValidator(
                images_base64=[img_b64],
                analysis_type='Positional',
                market_specialization='Crypto'
            )
            print("✅ 32x32 image request validation passed!")
            
        except Exception as e:
            print(f"❌ 32x32 image validation failed: {e}")
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ Error creating test image: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Testing async trading validation...")
    print(f"📁 Current directory: {os.getcwd()}")
    
    # Test configuration
    print(f"⚙️ MAX_UPLOAD_SIZE: {getattr(settings, 'MAX_UPLOAD_SIZE', 'Not set')}")
    print(f"⚙️ ALLOWED_IMAGE_TYPES: {getattr(settings, 'ALLOWED_IMAGE_TYPES', 'Not set')}")
    
    test_with_minimal_valid_image()
    test_with_real_image()
