@echo off
echo ========================================
echo 🎨 AI Trading Analysis - Frontend Startup
echo ========================================
echo.

:: Change to frontend directory
cd /d "%~dp0\Agent_Trading\next-frontend"

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if node_modules exists
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        echo Trying with yarn...
        yarn install
        if errorlevel 1 (
            echo ❌ Failed to install dependencies with both npm and yarn
            pause
            exit /b 1
        )
    )
    echo ✅ Dependencies installed successfully
)

:: Check if .env.local exists
if not exist ".env.local" (
    echo 🔧 Creating .env.local file...
    echo NEXT_PUBLIC_API_URL=http://localhost:8000 > .env.local
    echo NEXT_PUBLIC_ENVIRONMENT=development >> .env.local
    echo ✅ .env.local created with default settings
)

:: Start the development server
echo.
echo 🚀 Starting Next.js development server...
echo 📍 Frontend will be available at: http://localhost:3000
echo 📍 Backend API should be running at: http://localhost:8000
echo.
echo 🔥 Hot reload enabled - changes will update automatically
echo Press Ctrl+C to stop the server
echo ========================================

:: Start Next.js development server
npm run dev

:: If we get here, the server stopped
echo.
echo 🛑 Frontend server stopped
pause
