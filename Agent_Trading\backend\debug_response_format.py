#!/usr/bin/env python3
"""
Debug Response Format - Check what the frontend is actually receiving
"""

import os
import sys
import asyncio
import requests
import json
import time
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent))

def test_actual_frontend_behavior():
    """Test what the frontend is actually receiving from the backend"""
    
    print("🔍 Testing actual frontend behavior...")
    
    # Use the same endpoint the frontend calls
    url = "http://127.0.0.1:8000/api/v1/trading/analyze"
    
    # Test image path
    test_image_path = Path(__file__).parent.parent / "Testing_images" / "BTC_15min.png"
    
    if not test_image_path.exists():
        print(f"❌ Test image not found: {test_image_path}")
        return
    
    # Read and encode image to base64
    import base64
    with open(test_image_path, "rb") as f:
        image_data = base64.b64encode(f.read()).decode('utf-8')
    
    # Prepare request exactly like frontend does
    analysis_request = {
        "images_base64": [image_data],
        "analysis_type": "scalp",
        "market_specialization": "crypto",
        "preferred_model": "gemini-2.5-flash",
        "timeframes": ["15m"],
        "ticker_hint": "BTC/USDT",
        "context_hint": "Test analysis"
    }
    
    # Get token (you'll need to set this)
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiOTk2Y2Y2OTItODJmNy00NTBhLWJlYTYtY2I2Yzg2ZjkyZGVhIiwiZXhwIjoxNzI1NzU0NzUxLCJpYXQiOjE3MjU2NjgzNTEsInN1YiI6Ijg5OTZjZjY5Mi04MmY3LTQ1MGEtYmVhNi1jYjZjODZmOTJkZWEifQ.LZRTxNHyU5YvkrI0v5GqjYBw-_H4YQaO3-JmAXP7guw"
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    print("📤 Sending request to backend...")
    print(f"URL: {url}")
    print(f"Headers: {list(headers.keys())}")
    print(f"Body size: {len(json.dumps(analysis_request))} bytes")
    
    try:
        # Make request with timeout like frontend
        start_time = time.time()
        response = requests.post(
            url, 
            headers=headers, 
            json=analysis_request,
            timeout=180  # 3 minutes like frontend
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"⏱️ Request took: {execution_time:.2f} seconds")
        print(f"📡 Response status: {response.status_code}")
        print(f"📡 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Success! Response received:")
            try:
                response_data = response.json()
                print(f"Response keys: {list(response_data.keys())}")
                print(f"Success field: {response_data.get('success', 'Missing')}")
                
                if 'data' in response_data:
                    data = response_data['data']
                    print(f"Data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                print(f"Raw response: {response.text[:1000]}...")
                
        else:
            print(f"❌ Error response: {response.status_code}")
            print(f"Error text: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out after 3 minutes")
        
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - is the backend running?")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_actual_frontend_behavior()
