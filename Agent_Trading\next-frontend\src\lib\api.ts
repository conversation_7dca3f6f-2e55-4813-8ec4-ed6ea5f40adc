import axios from 'axios'
import { validateToken, getStoredToken, clearAuthData } from './auth-utils'
import { handleApiError, isAuthError } from './auth-error-handler'

// API client configuration
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for auth with token validation
api.interceptors.request.use((config) => {
  if (typeof window !== 'undefined') {
    const token = getStoredToken()
    if (token) {
      // Validate token before using it
      const validation = validateToken(token)
      if (validation.isValid) {
        config.headers.Authorization = `Bearer ${token}`
      } else {
        // Token is invalid, clear it and reject request
        clearAuthData()
        console.warn('Invalid token detected, clearing auth data:', validation.reason)
        return Promise.reject(new Error(`Invalid token: ${validation.reason}`))
      }
    }
  }
  return config
})

// Response interceptor for error handling with retry logic
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      // Try to refresh token first
      const refreshToken = localStorage.getItem('refresh_token')
      if (refreshToken) {
        try {
          const response = await axios.post(`${api.defaults.baseURL}/api/v1/auth/refresh`, {
            refresh_token: refreshToken
          })

          const { access_token } = response.data
          localStorage.setItem('access_token', access_token)

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`
          return api(originalRequest)
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError)
        }
      }

      // Handle auth error with proper error handling
      handleApiError(error)
      return Promise.reject(error)
    }

    // Check for other auth errors (403, 422, etc.)
    if (isAuthError(error)) {
      handleApiError(error)
      return Promise.reject(error)
    }

    // Handle network errors
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      console.error('Network connectivity issue:', error.message)
      throw new Error('Unable to connect to server. Please check your connection.')
    }

    return Promise.reject(error)
  }
)

// Types
export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  full_name: string
  tier?: string
}

export interface AnalysisOptions {
  analysis_type: 'Positional' | 'Scalp'
  market_specialization: 'Crypto' | 'Indian Market' | 'Others'
  ticker_hint?: string
  context_hint?: string
  timeframes?: string[]
}

export interface RAGQuery {
  query: string
  market_specialization?: string
  analysis_type?: string
  symbol?: string
  limit?: number
  similarity_threshold?: number
}

export interface Analysis {
  id: string
  analysis_type: string
  market_specialization: string
  detected_symbol?: string
  analysis_data: any
  trading_signals: any
  confidence_score?: number
  execution_time?: number
  created_at: string
}

// API functions
export const tradingApi = {
  // Authentication
  login: async (credentials: LoginCredentials) => {
    const response = await api.post('/api/v1/auth/login', credentials)
    if (response.data.access_token) {
      localStorage.setItem('access_token', response.data.access_token)
    }
    return response
  },

  register: (userData: RegisterData) => 
    api.post('/api/v1/auth/register', userData),

  logout: () => {
    localStorage.removeItem('access_token')
  },

  // User profile
  getProfile: () => api.get('/api/v1/auth/me'),

  // Trading analysis (backend expects JSON with base64 images)
  analyzeCharts: (requestData: any) =>
    api.post('/api/v1/trading/analyze', requestData, {
      headers: {
        'Content-Type': 'application/json',
      },
    }),

  getAnalysisHistory: (params?: any) => 
    api.get('/api/v1/trading/history', { params }),

  getAnalysisById: (id: string) => 
    api.get(`/api/v1/trading/analysis/${id}`),

  getUserStatistics: () => 
    api.get('/api/v1/trading/statistics'),

  // RAG search
  searchRAG: (query: RAGQuery) => 
    api.post('/api/v1/rag/search', query),

  getSimilarAnalyses: (analysisId: string) => 
    api.get(`/api/v1/rag/similar/${analysisId}`),

  getRecommendations: (params?: any) => 
    api.get('/api/v1/rag/recommendations', { params }),

  // Health check
  healthCheck: () => api.get('/health'),
}

export default api
