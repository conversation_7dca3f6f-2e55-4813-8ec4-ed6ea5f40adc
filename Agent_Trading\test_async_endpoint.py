#!/usr/bin/env python3
"""
Test Async Trading Endpoint
"""

import asyncio
import sys
import os
import json
import requests
import time
import base64
from pathlib import Path

# Add backend path
sys.path.append(str(Path(__file__).parent / "backend"))

def test_async_endpoint():
    """Test the new async trading endpoint"""
    
    print("🧪 Testing Async Trading Endpoint...")
    
    # Backend URL
    base_url = "http://127.0.0.1:8000"
    
    # Load test image (use available file)
    test_image_path = Path(__file__).parent / "Testing_images" / "Screenshot 2025-08-03 190956.png"
    
    if not test_image_path.exists():
        print(f"❌ Test image not found: {test_image_path}")
        return False
    
    # Encode image to base64
    with open(test_image_path, "rb") as f:
        image_b64 = base64.b64encode(f.read()).decode('utf-8')
    
    # Prepare request
    analysis_request = {
        "images_base64": [image_b64],
        "analysis_type": "scalp",
        "market_specialization": "crypto",
        "preferred_model": "gemini-2.5-flash",
        "timeframes": ["15m"],
        "ticker_hint": "BTC/USDT",
        "context_hint": "Test async analysis"
    }
    
    # Your token (update this with a valid one)
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiOTk2Y2Y2OTItODJmNy00NTBhLWJlYTYtY2I2Yzg2ZjkyZGVhIiwiZXhwIjoxNzI1NzU0NzUxLCJpYXQiOjE3MjU2NjgzNTEsInN1YiI6Ijg5OTZjZjY5Mi04MmY3LTQ1MGEtYmVhNi1jYjZjODZmOTJkZWEifQ.LZRTxNHyU5YvkrI0v5GqjYBw-_H4YQaO3-JmAXP7guw"
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    try:
        # Step 1: Start async analysis
        print("📤 Step 1: Starting async analysis...")
        
        start_response = requests.post(
            f"{base_url}/api/v1/trading/async/start",
            headers=headers,
            json=analysis_request,
            timeout=30
        )
        
        print(f"Start response: {start_response.status_code}")
        
        if start_response.status_code != 200:
            print(f"❌ Start failed: {start_response.text}")
            return False
        
        start_data = start_response.json()
        print(f"✅ Analysis started: {start_data}")
        
        session_id = start_data.get("session_id")
        if not session_id:
            print("❌ No session ID returned")
            return False
        
        # Step 2: Poll for result
        print(f"📥 Step 2: Polling for result (session: {session_id})...")
        
        max_attempts = 20
        for attempt in range(max_attempts):
            print(f"Attempt {attempt + 1}/{max_attempts}...")
            
            result_response = requests.get(
                f"{base_url}/api/v1/trading/async/result/{session_id}",
                headers={'Authorization': f'Bearer {token}'},
                timeout=10
            )
            
            if result_response.status_code != 200:
                print(f"❌ Result check failed: {result_response.status_code}")
                time.sleep(3)
                continue
            
            result_data = result_response.json()
            status = result_data.get("status", "unknown")
            
            print(f"Status: {status}")
            
            if status == "completed":
                print("🎉 Analysis completed successfully!")
                print(f"Result keys: {list(result_data.get('data', {}).keys())}")
                return True
            elif status == "failed":
                print(f"❌ Analysis failed: {result_data.get('error', 'Unknown error')}")
                return False
            elif status == "running":
                print("⏳ Still running, waiting...")
                time.sleep(5)
            else:
                print(f"❓ Unknown status: {status}")
                time.sleep(3)
        
        print("⏰ Timeout waiting for result")
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_async_endpoint()
    if success:
        print("\n✅ Async endpoint test PASSED!")
    else:
        print("\n❌ Async endpoint test FAILED!")
    
    # Also test if old endpoints are accessible
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/trading/analyze")
        print(f"\nOld trading endpoint status: {response.status_code}")
    except Exception as e:
        print(f"\nOld trading endpoint error: {e}")
