"""
Check the actual database schema and recent records.
"""

import sqlite3
import json
from pathlib import Path

def check_schema_and_data():
    """Check the database schema and recent data."""
    
    db_path = Path("trading_results.db")
    
    if not db_path.exists():
        print(f"❌ Database not found at: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📊 Found tables: {[table[0] for table in tables]}")
        
        for table_name in [table[0] for table in tables]:
            print(f"\n🔍 Table: {table_name}")
            
            # Get schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("   Columns:")
            for col in columns:
                print(f"     {col[1]} ({col[2]})")
            
            # Get recent records
            cursor.execute(f"SELECT * FROM {table_name} ORDER BY rowid DESC LIMIT 3")
            records = cursor.fetchall()
            
            print(f"   Recent records ({len(records)}):")
            for i, record in enumerate(records):
                print(f"     Record {i+1}: {record[:3]}...")  # Show first 3 fields
                
                # If this looks like it has analysis data, show more detail
                if len(record) > 3 and record[3]:  # Assuming result field might be 4th
                    try:
                        if isinstance(record[3], str) and record[3].startswith('{'):
                            result_data = json.loads(record[3])
                            print(f"       Result keys: {list(result_data.keys()) if isinstance(result_data, dict) else 'Not a dict'}")
                    except:
                        pass
                
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")

if __name__ == "__main__":
    check_schema_and_data()
