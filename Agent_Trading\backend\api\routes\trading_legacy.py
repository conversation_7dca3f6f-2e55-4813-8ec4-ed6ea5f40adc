"""
Secure trading analysis endpoints with comprehensive validation and monitoring.
Implements the LangGraph workflow with enterprise-grade security and e        except ExternalAPIError as e:
            logger.error(
                "External API error during analysis",
                error=str(e),
                user_id=current_user.id,
                correlation_id=correlation_id
            )
            raise HTTPExceptions.service_unavailable(
                "AI analysis service is temporarily unavailable. Please try again in a few minutes."
            )

        except ValidationError as e:
            logger.error(
                "Validation error during analysis",
                error=str(e),
                user_id=current_user.id,
                correlation_id=correlation_id
            )
            raise HTTPExceptions.bad_request(
                "Invalid chart format or analysis parameters. Please check your upload and try again."
            )
            
        except AnalysisError as e:
            logger.error(
                "Analysis error during processing",
                error=str(e),
                user_id=current_user.id,
                correlation_id=correlation_id
            )
            # Check if it's a model or quota related error
            error_msg = str(e).lower()
            if "quota" in error_msg or "rate limit" in error_msg:
                raise HTTPExceptions.service_unavailable(
                    "Analysis service is experiencing high demand. Please try again in a few minutes."
                )
            elif "model" in error_msg or "api" in error_msg:
                raise HTTPExceptions.service_unavailable(
                    "AI service is temporarily unavailable. Please try again later."
                )
            else:
                raise HTTPExceptions.internal_server_error(
                    "Unable to complete analysis. Please try again or contact support if the issue persists."
                )

        except Exception as e:
            logger.error(
                "Unexpected error during analysis",
                error=str(e),
                user_id=current_user.id,
                correlation_id=correlation_id
            )
            raise HTTPExceptions.internal_server_error(
                "Something went wrong during analysis. Our team has been notified. Please try again in a few minutes."
            )"""

import time
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from core.security import get_current_active_user, require_tier
from models.user import User
from core.exceptions import (
    ValidationError, AnalysisError, ExternalAPIError, RAGSystemError,
    HTTPExceptions
)
from core.logging import get_logger, log_function_call
from core.config import settings
from core.database import get_db, AsyncSession
from api.validators import AnalyzeRequestValidator, ImageValidator
from services.trading_service import TradingAnalysisService
from helpers.progress_tracker import (
    get_progress_tracker, 
    get_progress_broadcaster,
    WorkflowStep
)


logger = get_logger("trading")
router = APIRouter(prefix="/trading", tags=["trading"])


class AnalyzeResponse(BaseModel):
    """Trading analysis response model."""
    success: bool
    data: Dict[str, Any]
    error: str = None
    metadata: Dict[str, Any] = None
    execution_time: float = None
    session_id: str = None  # For progress tracking


class AnalysisMetadata(BaseModel):
    """Analysis metadata model."""
    model_config = {"protected_namespaces": ()}

    user_id: str
    tier: str
    model_used: str
    execution_time: float
    correlation_id: str
    timestamp: str


class StandardResponse(BaseModel):
    """Standard API response model for consistency."""
    success: bool
    data: Dict[str, Any] = {}
    error: str = None
    total: int = None
    limit: int = None
    offset: int = None


@router.post("/analyze", response_model=AnalyzeResponse)
@log_function_call(logger)
async def analyze_trading_charts(
    request: AnalyzeRequestValidator,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> AnalyzeResponse:
    """
    Analyze trading charts using AI with comprehensive security and validation.
    
    **Security Features:**
    - JWT authentication required
    - Tier-based access control
    - Rate limiting per user tier
    - Input validation and sanitization
    - File size and format validation
    
    **Analysis Features:**
    - Multi-chart analysis support
    - LangGraph workflow integration
    - RAG-enhanced historical context
    - Structured trading signals output
    - Model fallback for reliability
    """
    start_time = time.time()

    # Create progress tracking session
    progress_tracker = get_progress_tracker()
    progress_broadcaster = get_progress_broadcaster()
    session_id = progress_tracker.create_session()

    try:
        # Step 1: Chart Upload Complete
        update = progress_tracker.update_progress(
            session_id, 
            WorkflowStep.CHART_UPLOAD,
            f"Received {len(request.images_base64)} chart images for analysis",
            details={"image_count": len(request.images_base64), "analysis_type": request.analysis_type}
        )
        await progress_broadcaster.broadcast_update(session_id, update)
        
        # Log analysis request
        logger.info(
            "Trading analysis request started",
            user_id=current_user.id,
            user_tier=current_user.tier,
            analysis_type=request.analysis_type,
            market_specialization=request.market_specialization,
            image_count=len(request.images_base64),
            preferred_model=request.preferred_model,
            session_id=session_id
        )
        
        # Validate user tier permissions
        if request.preferred_model == "gemini-2.5-pro" and current_user.tier == "free":
            logger.warning(
                "Free tier user attempted to use premium model",
                user_id=current_user.id,
                requested_model=request.preferred_model
            )
            # Downgrade to flash model for free tier
            request.preferred_model = "gemini-2.5-flash"
        
        # Validate and process images
        chart_images_bytes: List[bytes] = []
        for i, img_b64 in enumerate(request.images_base64):
            try:
                image_bytes = ImageValidator.validate_base64_image(img_b64)
                chart_images_bytes.append(image_bytes)

                logger.debug(
                    f"Image {i+1} validated successfully",
                    size_bytes=len(image_bytes),
                    user_id=current_user.id
                )

            except ValidationError as e:
                logger.error(
                    f"Image {i+1} validation failed",
                    error=str(e),
                    user_id=current_user.id
                )
                raise HTTPExceptions.validation_error(
                    f"Image {i+1} validation failed: {str(e)}"
                )

        # Prepare user query
        user_query = f"{request.analysis_type} trading analysis for {request.market_specialization} market"

        if request.timeframes:
            user_query += f" focusing on {', '.join(request.timeframes)} timeframes"
        if request.ticker_hint:
            user_query += f" | symbol hint: {request.ticker_hint}"
        if request.context_hint:
            user_query += f" | context: {request.context_hint}"

        # Execute analysis using enhanced trading service
        try:
            # Step 2: Starting Analysis
            update = progress_tracker.update_progress(
                session_id,
                WorkflowStep.VISION_ANALYSIS,
                "Starting AI vision analysis and tool selection...",
                details={"query": user_query}
            )
            await progress_broadcaster.broadcast_update(session_id, update)

            # Initialize trading service with error handling
            try:
                trading_service = TradingAnalysisService(
                    db=db,
                    google_api_key=settings.google_api_key
                )
                logger.info(f"Trading service initialized successfully for user {current_user.id}")
            except Exception as e:
                logger.error(f"Failed to initialize trading service: {e}")
                raise HTTPExceptions.service_unavailable(f"Analysis service initialization failed: {str(e)}")

            # Perform analysis with detailed error logging
            try:
                result = await trading_service.perform_analysis(
                    user=current_user,
                    chart_images=chart_images_bytes,
                    analysis_type=request.analysis_type,
                    market_specialization=request.market_specialization,
                    user_query=user_query,
                    preferred_model=request.main_model or request.preferred_model,
                    context_hint=request.context_hint,
                    timeframes=request.timeframes,
                    ticker_hint=request.ticker_hint,
                    session_id=session_id,  # Pass session_id for progress tracking
                    progress_tracker=progress_tracker,
                    progress_broadcaster=progress_broadcaster
                )
                logger.info(f"Analysis completed successfully for user {current_user.id}")
            except Exception as e:
                logger.error(f"Analysis execution failed: {e}")
                import traceback
                logger.error(f"Full traceback: {traceback.format_exc()}")
                raise
            
            # Step Final: Analysis Complete
            update = progress_tracker.update_progress(
                session_id,
                WorkflowStep.COMPLETE,
                "Analysis completed successfully!",
                details={"execution_time": time.time() - start_time}
            )
            await progress_broadcaster.broadcast_update(session_id, update)
            
        except ExternalAPIError as e:
            logger.error(
                "External API error during analysis",
                error=str(e),
                user_id=current_user.id
            )
            raise HTTPExceptions.service_unavailable("AI analysis service temporarily unavailable")

        except AnalysisError as e:
            logger.error(
                "Analysis processing error",
                error=str(e),
                user_id=current_user.id
            )
            raise HTTPExceptions.internal_server_error("Analysis processing failed")

        except Exception as e:
            logger.error(
                "Unexpected error during analysis",
                error=str(e),
                user_id=current_user.id
            )
            raise HTTPExceptions.internal_server_error("Analysis failed due to unexpected error")

        # Extract result data
        if result["success"]:
            analysis_data = result["data"]
            execution_time = result["execution_time"]

            # Prepare metadata
            metadata = {
                "user_id": str(current_user.id),
                "user_tier": current_user.tier,
                "execution_time": execution_time,
                "model_used": result.get("model_used", "unknown"),
                "image_count": len(chart_images_bytes),
                "analysis_type": request.analysis_type,
                "market_specialization": request.market_specialization,
                "analysis_id": result.get("analysis_id"),
                "cached": result.get("cached", False)
            }

            # Log successful analysis
            logger.info(
                "Trading analysis completed successfully",
                user_id=current_user.id,
                execution_time=execution_time,
                model_used=metadata["model_used"],
                success=True
            )

            return AnalyzeResponse(
                success=True,
                data=analysis_data,
                metadata=metadata,
                execution_time=execution_time,
                session_id=session_id
            )
        else:
            raise AnalysisError("Analysis service returned failure")
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    
    except Exception as e:
        execution_time = time.time() - start_time

        logger.error(
            "Trading analysis failed with unexpected error",
            error=str(e),
            user_id=current_user.id,
            execution_time=execution_time
        )

        # Import traceback for detailed error logging
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Raise proper HTTP exception instead of returning error response
        raise HTTPExceptions.internal_server_error(
            f"Analysis failed due to unexpected error"
        )


@router.get("/history", response_model=StandardResponse)
async def get_analysis_history(
    limit: int = 50,
    offset: int = 0,
    analysis_type: Optional[str] = None,
    market_specialization: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> StandardResponse:
    """Get user's trading analysis history."""
    try:
        trading_service = TradingAnalysisService(db)

        history = await trading_service.get_user_analysis_history(
            user_id=str(current_user.id),
            limit=limit,
            offset=offset,
            analysis_type=analysis_type,
            market_specialization=market_specialization
        )

        return StandardResponse(
            success=True,
            data={"history": history},
            total=len(history),
            limit=limit,
            offset=offset
        )

    except Exception as e:
        logger.error(f"Failed to get analysis history for user {current_user.id}", error=e)
        return StandardResponse(
            success=False,
            data={},
            error=str(e)
        )


@router.get("/analysis/{analysis_id}", response_model=StandardResponse)
async def get_analysis_by_id(
    analysis_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> StandardResponse:
    """Get specific analysis by ID."""
    try:
        trading_service = TradingAnalysisService(db)

        analysis = await trading_service.get_analysis_by_id(
            analysis_id=analysis_id,
            user_id=str(current_user.id)
        )

        if not analysis:
            raise HTTPExceptions.not_found("Analysis not found")

        return StandardResponse(
            success=True,
            data={"analysis": analysis}
        )

    except Exception as e:
        logger.error(f"Failed to get analysis {analysis_id}", error=e)
        raise HTTPExceptions.internal_server_error("Failed to retrieve analysis")


@router.get("/statistics", response_model=StandardResponse)
async def get_user_statistics(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> StandardResponse:
    """Get user's trading analysis statistics."""
    try:
        trading_service = TradingAnalysisService(db)

        stats = await trading_service.get_user_statistics(str(current_user.id))

        return StandardResponse(
            success=True,
            data={"statistics": stats}
        )

    except Exception as e:
        logger.error(f"Failed to get statistics for user {current_user.id}", error=e)
        return StandardResponse(
            success=False,
            data={},
            error=str(e)
        )


