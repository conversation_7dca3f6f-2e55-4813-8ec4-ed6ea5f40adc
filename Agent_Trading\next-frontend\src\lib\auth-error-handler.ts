/**
 * Authentication Error Handler
 * Handles authentication failures and automatic redirects
 */

import { clearAuthData } from './auth-utils'
import toast from 'react-hot-toast'

export interface AuthError {
  status: number
  message: string
  code?: string
}

/**
 * Handle authentication errors with automatic logout and redirect
 */
export function handleAuthError(error: AuthError, router?: any): void {
  console.error('Authentication error:', error)

  // Clear authentication data
  clearAuthData()

  // Show appropriate error message
  let message = 'Authentication failed'
  
  switch (error.status) {
    case 401:
      message = 'Your session has expired. Please log in again.'
      break
    case 403:
      message = 'Access denied. Please check your permissions.'
      break
    case 422:
      message = 'Invalid authentication data. Please log in again.'
      break
    default:
      if (error.message.includes('expired')) {
        message = 'Your session has expired. Please log in again.'
      } else if (error.message.includes('invalid')) {
        message = 'Invalid session. Please log in again.'
      } else {
        message = error.message || 'Authentication failed. Please log in again.'
      }
  }

  // Show toast notification
  toast.error(message, {
    duration: 5000,
    position: 'top-center',
  })

  // Redirect to login page
  if (router) {
    router.push('/login?reason=session_expired')
  } else if (typeof window !== 'undefined') {
    // Fallback redirect
    window.location.href = '/login?reason=session_expired'
  }
}

/**
 * Check if error is authentication related
 */
export function isAuthError(error: any): boolean {
  if (!error) return false

  const status = error.response?.status || error.status
  const message = error.response?.data?.detail || error.message || ''

  // Check status codes
  if ([401, 403, 422].includes(status)) {
    return true
  }

  // Check error messages
  const authErrorMessages = [
    'token',
    'expired',
    'invalid',
    'unauthorized',
    'forbidden',
    'authentication',
    'session'
  ]

  return authErrorMessages.some(keyword => 
    message.toLowerCase().includes(keyword)
  )
}

/**
 * Extract auth error details from various error formats
 */
export function extractAuthError(error: any): AuthError {
  const status = error.response?.status || error.status || 500
  const message = error.response?.data?.detail || 
                 error.response?.data?.message || 
                 error.message || 
                 'Unknown authentication error'
  
  const code = error.response?.data?.code || error.code

  return { status, message, code }
}

/**
 * Handle API response errors with authentication check
 */
export function handleApiError(error: any, router?: any): void {
  if (isAuthError(error)) {
    const authError = extractAuthError(error)
    handleAuthError(authError, router)
  } else {
    // Handle non-auth errors
    const message = error.response?.data?.detail || 
                   error.message || 
                   'An unexpected error occurred'
    
    toast.error(message, {
      duration: 4000,
    })
  }
}

/**
 * Validate token before making API calls
 */
export function validateBeforeApiCall(): { isValid: boolean; error?: AuthError } {
  const token = typeof window !== 'undefined' ? localStorage.getItem('access_token') : null
  
  if (!token) {
    return {
      isValid: false,
      error: {
        status: 401,
        message: 'No authentication token found. Please log in.'
      }
    }
  }

  // Import here to avoid circular dependencies
  const { validateToken } = require('./auth-utils')
  const validation = validateToken(token)
  
  if (!validation.isValid) {
    return {
      isValid: false,
      error: {
        status: 401,
        message: validation.reason || 'Invalid authentication token'
      }
    }
  }

  return { isValid: true }
}
