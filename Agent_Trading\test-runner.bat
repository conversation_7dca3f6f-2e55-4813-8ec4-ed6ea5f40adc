@echo off
REM AI Trading Analysis - Environment Setup and Testing Script
REM Ensures proper conda environment activation and runs tests

echo ========================================
echo AI Trading Analysis - Test Suite
echo ========================================

REM Activate the AIWEB_ENV conda environment
echo 🔧 Activating AIWEB_ENV conda environment...
call conda activate AIWEB_ENV

REM Check if environment activation was successful
if errorlevel 1 (
    echo ❌ Failed to activate AIWEB_ENV environment
    echo Please ensure conda is installed and AIWEB_ENV exists
    pause
    exit /b 1
)

echo ✅ AIWEB_ENV environment activated successfully

REM Change to backend directory
cd /d "C:\Agent_Trading\Agent_Trading\backend"

REM Show current environment info
echo.
echo 📊 Environment Information:
echo Current directory: %CD%
conda info --envs | findstr "*"
python --version

echo.
echo ========================================
echo Choose an option:
echo ========================================
echo 1. Run Debug Trading Analysis
echo 2. Run Comprehensive API Tests  
echo 3. Start Backend Server
echo 4. Check Database Connection
echo 5. Run All Tests
echo 6. Exit
echo ========================================

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" (
    echo.
    echo 🔍 Running Debug Trading Analysis...
    python debug_trading_analysis.py
) else if "%choice%"=="2" (
    echo.
    echo 🧪 Running Comprehensive API Tests...
    python test_comprehensive_api.py
) else if "%choice%"=="3" (
    echo.
    echo 🚀 Starting Backend Server...
    echo Note: Make sure PostgreSQL and Redis are running
    uvicorn main:app --reload --host 0.0.0.0 --port 8000
) else if "%choice%"=="4" (
    echo.
    echo 🔗 Checking Database Connection...
    python -c "from core.database import test_connection; import asyncio; asyncio.run(test_connection())"
) else if "%choice%"=="5" (
    echo.
    echo 🔍 Running Debug Trading Analysis...
    python debug_trading_analysis.py
    echo.
    echo 🧪 Running Comprehensive API Tests...
    python test_comprehensive_api.py
    echo.
    echo ✅ All tests completed!
) else if "%choice%"=="6" (
    echo.
    echo 👋 Goodbye!
    exit /b 0
) else (
    echo.
    echo ❌ Invalid choice. Please run the script again.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Test execution completed!
echo ========================================
pause
