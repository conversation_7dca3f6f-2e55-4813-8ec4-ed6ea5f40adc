"""
Debug the async result endpoint to see exactly what data is being returned.
"""

import requests
import json
import sys
from pathlib import Path

def debug_async_result():
    """Debug what the async result endpoint is returning."""
    
    print("🔍 Debugging async result endpoint...")
    
    # Use the session ID from the logs
    session_id = "async_c33a7317bb57"  # From the user's logs
    
    base_url = "http://127.0.0.1:8000"
    
    # Get a valid token (you'll need to login first)
    print("⚠️  Note: You need to be logged in to get a valid token")
    print("   You can get it from localStorage.getItem('access_token') in browser")
    
    # For now, let's just check the endpoint structure
    try:
        response = requests.get(f"{base_url}/api/v1/trading/async/result/{session_id}")
        
        print(f"📡 Response status: {response.status_code}")
        print(f"📡 Response headers: {dict(response.headers)}")
        
        if response.status_code == 401:
            print("🔑 Authentication required - endpoint is protected correctly")
            print("💡 To test with token, add: headers={'Authorization': 'Bearer YOUR_TOKEN'}")
        elif response.status_code == 200:
            result = response.json()
            print("📊 Response structure:")
            print(json.dumps(result, indent=2))
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def analyze_logs():
    """Analyze the logs to understand the data flow."""
    
    print("\n🔍 Analyzing the backend logs...")
    
    print("From the logs, I can see:")
    print("✅ Analysis completed successfully")
    print("✅ Session ID: async_c33a7317bb57")
    print("✅ Database record ID: 27ccb1be-6b74-4e4c-bcb6-5d81f3b66be7")
    print("✅ Analysis data stored in database")
    print("✅ Result endpoint returns 200 OK")
    
    print("\n🤔 The issue seems to be:")
    print("1. Backend analysis completes and stores data correctly")
    print("2. Frontend polls and gets 200 OK response")
    print("3. BUT the data transformation or display is not working")
    
    print("\n💡 Possible issues:")
    print("- The resultData.data structure doesn't match what transformLangGraphResponse expects")
    print("- The transformed result is not being displayed correctly by EnhancedAnalysisResults")
    print("- The static content you see might be placeholder/fallback content")

if __name__ == "__main__":
    debug_async_result()
    analyze_logs()
