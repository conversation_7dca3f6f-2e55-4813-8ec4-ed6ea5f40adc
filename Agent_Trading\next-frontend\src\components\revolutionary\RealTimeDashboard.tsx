'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { GlassCard, PremiumButton } from '@/components/ui/PremiumComponents'
import { AnimatedContainer } from '@/components/animations/PremiumAnimations'
import { useAuth } from '@/components/providers/AuthProvider'
import { tradingApi } from '@/lib/api'
import { transformUserStatistics } from '@/lib/dataTransformers'
import { 
  TrendingUp, TrendingDown, DollarSign, Activity, 
  Brain, Target, Award, Clock, RefreshCw, AlertCircle,
  BarChart3, PieChart, LineChart, Globe, Zap, Users
} from 'lucide-react'

interface RealTimeMetrics {
  portfolio_value: number
  daily_pnl: number
  daily_pnl_percentage: number
  total_analyses: number
  successful_predictions: number
  win_rate: number
  avg_confidence: number
  active_positions: number
  market_exposure: number
  risk_score: number
}

interface MarketData {
  symbol: string
  price: number
  change: number
  change_percentage: number
  volume: string
  market_cap?: string
  last_updated: string
}

interface RecentAnalysis {
  id: string
  symbol: string
  recommendation: 'BUY' | 'SELL' | 'HOLD'
  confidence: number
  created_at: string
  status: 'active' | 'closed' | 'pending'
  pnl?: number
}

interface AIInsight {
  type: 'opportunity' | 'warning' | 'info' | 'success'
  title: string
  message: string
  confidence: number
  timestamp: string
  actionable: boolean
}

export const RealTimeDashboard: React.FC = () => {
  const { user } = useAuth()
  const [metrics, setMetrics] = useState<RealTimeMetrics | null>(null)
  const [marketData, setMarketData] = useState<MarketData[]>([])
  const [recentAnalyses, setRecentAnalyses] = useState<RecentAnalysis[]>([])
  const [aiInsights, setAiInsights] = useState<AIInsight[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Fetch real-time dashboard data
  const fetchDashboardData = async () => {
    if (!user) return

    try {
      setIsLoading(true)

      // Fetch user statistics and metrics
      const [statsResponse, historyResponse] = await Promise.all([
        tradingApi.getUserStatistics(),
        tradingApi.getAnalysisHistory({ limit: 5 })
      ])

      if (statsResponse.data) {
        // Use the data transformer to handle free tier limitations
        const transformedStats = transformUserStatistics(statsResponse.data)
        setMetrics({
          // Focus on analysis metrics rather than trading metrics for an analysis dashboard
          portfolio_value: 0, // Not applicable for analysis dashboard
          daily_pnl: 0, // Not applicable for analysis dashboard  
          daily_pnl_percentage: 0, // Not applicable for analysis dashboard
          total_analyses: transformedStats.total_analyses,
          successful_predictions: transformedStats.successful_predictions,
          win_rate: transformedStats.win_rate || 0, // Use actual win rate
          avg_confidence: transformedStats.avg_confidence,
          active_positions: transformedStats.total_analyses, // Reinterpret as total analyses
          market_exposure: transformedStats.avg_confidence, // Reinterpret as avg confidence
          risk_score: 100 - (transformedStats.avg_confidence || 0) // Inverse of confidence as risk
        })
      }

      if (historyResponse.data?.analyses) {
        setRecentAnalyses(historyResponse.data.analyses.map((analysis: any) => ({
          id: analysis.id,
          symbol: analysis.detected_symbol || 'Unknown',
          recommendation: analysis.trading_signals?.recommendation || 'HOLD',
          confidence: analysis.confidence_score || 0,
          created_at: analysis.created_at,
          status: analysis.status === 'completed' ? 'active' : 'pending',
          // Remove simulated P&L - this is an analysis dashboard, not a trading platform
          // pnl: Math.random() * 1000 - 500 // Simulated P&L
        })))
      }

      // Generate AI insights based on real data
      if (statsResponse.data) {
        const insights: AIInsight[] = []
        const stats = statsResponse.data

        if (stats.avg_confidence > 85) {
          insights.push({
            type: 'success',
            title: 'High Confidence Streak',
            message: `Your recent analyses show ${stats.avg_confidence.toFixed(1)}% average confidence. Great job!`,
            confidence: stats.avg_confidence,
            timestamp: new Date().toISOString(),
            actionable: false
          })
        }

        if (stats.total_analyses > 10) {
          insights.push({
            type: 'opportunity',
            title: 'Portfolio Diversification',
            message: 'Consider analyzing different market sectors to reduce risk exposure.',
            confidence: 78,
            timestamp: new Date().toISOString(),
            actionable: true
          })
        }

        if (stats.avg_confidence < 70) {
          insights.push({
            type: 'warning',
            title: 'Low Confidence Alert',
            message: 'Recent analyses show lower confidence. Review market conditions.',
            confidence: stats.avg_confidence,
            timestamp: new Date().toISOString(),
            actionable: true
          })
        }

        setAiInsights(insights)
      }

      setLastUpdated(new Date())
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
      // Set fallback data to prevent empty dashboard
      setMetrics({
        portfolio_value: 0,
        daily_pnl: 0,
        daily_pnl_percentage: 0,
        total_analyses: 0,
        successful_predictions: 0,
        win_rate: 0,
        avg_confidence: 0,
        active_positions: 0,
        market_exposure: 0,
        risk_score: 0
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Auto-refresh functionality
  useEffect(() => {
    fetchDashboardData()

    if (autoRefresh) {
      const interval = setInterval(fetchDashboardData, 30000) // Refresh every 30 seconds
      return () => clearInterval(interval)
    }
  }, [user, autoRefresh])

  // Listen for analysis completion events to refresh dashboard
  useEffect(() => {
    const handleAnalysisCompleted = () => {
      console.log('📊 Analysis completed - refreshing dashboard data')
      fetchDashboardData()
    }

    window.addEventListener('analysis-completed', handleAnalysisCompleted)
    return () => window.removeEventListener('analysis-completed', handleAnalysisCompleted)
  }, [])

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return TrendingUp
      case 'warning': return AlertCircle
      case 'success': return Award
      default: return Brain
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'opportunity': return 'green'
      case 'warning': return 'red'
      case 'success': return 'blue'
      default: return 'purple'
    }
  }

  if (isLoading && !metrics) {
    return (
      <div className="min-h-screen glass-effect flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-indigo-400" />
          <p className="text-white/90">Loading your real-time dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Trading Dashboard</h1>
          <p className="text-white/60">Real-time insights and performance metrics</p>
        </div>
        <div className="flex items-center gap-3">
          <div className="text-sm text-white/60">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </div>
          <PremiumButton
            onClick={fetchDashboardData}
            disabled={isLoading}
            className="px-4 py-2"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </PremiumButton>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <AnimatedContainer animation="scaleIn">
          <GlassCard variant="premium" className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Total Analyses</p>
                <p className="text-2xl font-bold text-indigo-400">
                  {metrics?.total_analyses || 0}
                </p>
                <p className="text-xs text-indigo-400 flex items-center gap-1 mt-1">
                  <BarChart3 className="h-3 w-3" />
                  All time
                </p>
              </div>
              <div className="p-3 rounded-xl bg-indigo-500/20">
                <BarChart3 className="h-6 w-6 text-indigo-400" />
              </div>
            </div>
          </GlassCard>
        </AnimatedContainer>

        <AnimatedContainer animation="scaleIn" delay={0.1}>
          <GlassCard variant="premium" className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Win Rate</p>
                <p className="text-2xl font-bold text-green-400">
                  {metrics?.win_rate.toFixed(1) || 0}%
                </p>
                <p className="text-xs text-green-400 flex items-center gap-1 mt-1">
                  <Target className="h-3 w-3" />
                  {metrics?.successful_predictions || 0} successful
                </p>
              </div>
              <div className="p-3 rounded-xl bg-green-500/20">
                <Target className="h-6 w-6 text-green-400" />
              </div>
            </div>
          </GlassCard>
        </AnimatedContainer>

        <AnimatedContainer animation="scaleIn" delay={0.2}>
          <GlassCard variant="premium" className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Avg Confidence</p>
                <p className="text-2xl font-bold text-purple-400">
                  {metrics?.avg_confidence.toFixed(0) || 0}%
                </p>
                <p className="text-xs text-purple-400 flex items-center gap-1 mt-1">
                  <Brain className="h-3 w-3" />
                  AI powered
                </p>
              </div>
              <div className="p-3 rounded-xl bg-purple-500/20">
                <Brain className="h-6 w-6 text-purple-400" />
              </div>
            </div>
          </GlassCard>
        </AnimatedContainer>

        <AnimatedContainer animation="scaleIn" delay={0.3}>
          <GlassCard variant="premium" className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/60 text-sm">Risk Score</p>
                <p className="text-2xl font-bold text-yellow-400">
                  {metrics?.risk_score.toFixed(0) || 0}
                </p>
                <p className="text-xs text-yellow-400 flex items-center gap-1 mt-1">
                  <Activity className="h-3 w-3" />
                  {(metrics?.risk_score || 0) < 30 ? 'Low' : (metrics?.risk_score || 0) < 60 ? 'Medium' : 'High'} risk
                </p>
              </div>
              <div className="p-3 rounded-xl bg-yellow-500/20">
                <Activity className="h-6 w-6 text-yellow-400" />
              </div>
            </div>
          </GlassCard>
        </AnimatedContainer>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Analyses */}
        <div className="lg:col-span-2">
          <AnimatedContainer animation="slideUp">
            <GlassCard variant="premium" className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-white">Recent Analyses</h3>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span className="text-xs text-white/60">Live</span>
                </div>
              </div>
              <div className="space-y-4">
                {recentAnalyses.length > 0 ? (
                  recentAnalyses.map((analysis) => (
                    <div key={analysis.id} className="flex items-center justify-between p-4 glass-effect rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className={`p-2 rounded-lg ${
                          analysis.recommendation === 'BUY' ? 'bg-green-500/20' : 
                          analysis.recommendation === 'SELL' ? 'bg-red-500/20' : 'bg-yellow-500/20'
                        }`}>
                          {analysis.recommendation === 'BUY' ? (
                            <TrendingUp className="h-4 w-4 text-green-400" />
                          ) : analysis.recommendation === 'SELL' ? (
                            <TrendingDown className="h-4 w-4 text-red-400" />
                          ) : (
                            <Activity className="h-4 w-4 text-yellow-400" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium text-white">{analysis.symbol}</p>
                          <p className="text-sm text-white/60">
                            {analysis.recommendation} • {analysis.confidence.toFixed(0)}% confidence
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-medium ${
                          analysis.status === 'active' ? 'text-green-400' : 
                          analysis.status === 'pending' ? 'text-yellow-400' : 'text-gray-400'
                        }`}>
                          {analysis.status === 'active' ? 'Completed' : 
                           analysis.status === 'pending' ? 'Processing' : 'Draft'}
                        </p>
                        <p className="text-sm text-white/60">
                          {new Date(analysis.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <BarChart3 className="h-12 w-12 text-white/20 mx-auto mb-4" />
                    <p className="text-white/60">No analyses yet. Start by uploading a chart!</p>
                  </div>
                )}
              </div>
            </GlassCard>
          </AnimatedContainer>
        </div>

        {/* AI Insights */}
        <AnimatedContainer animation="slideUp" delay={0.1}>
          <GlassCard variant="premium" className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white">AI Insights</h3>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            </div>
            <div className="space-y-4">
              {aiInsights.length > 0 ? (
                aiInsights.map((insight, index) => {
                  const Icon = getInsightIcon(insight.type)
                  const color = getInsightColor(insight.type)
                  
                  return (
                    <div key={index} className={`p-4 bg-${color}-500/10 rounded-lg border border-${color}-500/20`}>
                      <div className="flex items-start gap-3">
                        <Icon className={`h-5 w-5 text-${color}-400 mt-0.5 flex-shrink-0`} />
                        <div className="flex-1">
                          <h4 className={`text-${color}-400 font-medium text-sm`}>{insight.title}</h4>
                          <p className="text-white/80 text-xs mt-1">{insight.message}</p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-white/60">
                              {insight.confidence}% confidence
                            </span>
                            {insight.actionable && (
                              <span className="text-xs px-2 py-1 bg-white/10 rounded text-white/80">
                                Actionable
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })
              ) : (
                <div className="text-center py-8">
                  <Brain className="h-12 w-12 text-white/20 mx-auto mb-4" />
                  <p className="text-white/60">AI insights will appear here based on your trading activity</p>
                </div>
              )}
            </div>
          </GlassCard>
        </AnimatedContainer>
      </div>
    </div>
  )
}
