"""
Check the database record to see what data is actually stored.
"""

import sqlite3
import json
from pathlib import Path

def check_database_record():
    """Check what's actually stored in the database."""
    
    db_path = Path("C:/Agent_Trading/Agent_Trading/trading_results.db")
    
    if not db_path.exists():
        print(f"❌ Database not found at: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get the latest record
        cursor.execute("""
            SELECT id, session_id, status, result, created_at 
            FROM analysis_results 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        records = cursor.fetchall()
        
        print(f"📊 Found {len(records)} recent records:")
        
        for i, record in enumerate(records):
            id_val, session_id, status, result, created_at = record
            
            print(f"\n📋 Record {i+1}:")
            print(f"   ID: {id_val}")
            print(f"   Session ID: {session_id}")
            print(f"   Status: {status}")
            print(f"   Created: {created_at}")
            
            if result:
                try:
                    result_data = json.loads(result)
                    print(f"   Result structure:")
                    
                    # Show the keys and structure
                    if isinstance(result_data, dict):
                        for key in result_data.keys():
                            value = result_data[key]
                            if isinstance(value, dict):
                                print(f"     {key}: dict with keys {list(value.keys())}")
                            elif isinstance(value, list):
                                print(f"     {key}: list with {len(value)} items")
                            else:
                                print(f"     {key}: {type(value).__name__}")
                    else:
                        print(f"     Type: {type(result_data).__name__}")
                        
                except Exception as e:
                    print(f"   ❌ Error parsing result JSON: {e}")
                    print(f"   Raw result (first 200 chars): {result[:200]}...")
            else:
                print(f"   Result: None")
                
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")

if __name__ == "__main__":
    check_database_record()
