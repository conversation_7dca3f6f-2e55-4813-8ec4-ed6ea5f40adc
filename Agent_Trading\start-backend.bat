@echo off
echo ========================================
echo 🚀 AI Trading Analysis - Backend Startup
echo ========================================
echo.

:: Change to backend directory
cd /d "%~dp0\Agent_Trading\backend"

:: Check if virtual environment exists
if not exist "venv" (
    echo ❌ Virtual environment not found!
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        echo Please ensure Python is installed and in PATH
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created successfully
)

:: Activate virtual environment
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat

:: Check if requirements are installed
echo 🔄 Checking dependencies...
python -c "import fastapi" 2>nul
if errorlevel 1 (
    echo 📦 Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
)

:: Set environment variables for development
echo 🔧 Setting up environment variables...
set ENVIRONMENT=development
set DEBUG=true
set DATABASE_URL=postgresql://postgres:password@localhost:5432/trading_db
set REDIS_URL=redis://localhost:6379
set GOOGLE_API_KEY=your_google_api_key_here

:: Check if config.json exists
if not exist "config.json" (
    echo ⚠️  config.json not found, creating default...
    echo {
    echo   "google_api_key": "your_google_api_key_here",
    echo   "database_url": "postgresql://postgres:password@localhost:5432/trading_db",
    echo   "redis_url": "redis://localhost:6379",
    echo   "environment": "development"
    echo } > config.json
    echo ✅ Default config.json created
    echo ⚠️  Please update config.json with your actual API keys
)

:: Start the backend server
echo.
echo 🚀 Starting FastAPI backend server...
echo 📍 Backend will be available at: http://localhost:8000
echo 📍 API Documentation: http://localhost:8000/docs
echo 📍 Alternative docs: http://localhost:8000/redoc
echo.
echo Press Ctrl+C to stop the server
echo ========================================

:: Start with auto-reload for development
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload --log-level info

:: If we get here, the server stopped
echo.
echo 🛑 Backend server stopped
pause
