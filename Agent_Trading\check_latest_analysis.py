"""
Check the latest analysis result in detail.
"""

import sqlite3
import json
from pathlib import Path

def check_latest_analysis():
    """Check the latest analysis result in detail."""
    
    db_path = Path("trading_results.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get the latest analysis result
        cursor.execute("""
            SELECT * FROM analysis_results 
            ORDER BY timestamp DESC 
            LIMIT 1
        """)
        
        record = cursor.fetchone()
        
        if record:
            print("📊 Latest Analysis Result:")
            
            # Column names based on schema
            columns = [
                'id', 'timestamp', 'symbol', 'market_type', 'analysis_mode',
                'chart_path', 'analysis_notes', 'trading_signals', 'tool_results',
                'confidence_score', 'entry_levels', 'stop_loss', 'take_profit',
                'risk_reward_ratio', 'status'
            ]
            
            for i, col in enumerate(columns):
                value = record[i] if i < len(record) else None
                
                print(f"  {col}: {value}")
                
                # Parse JSON fields if they exist
                if col in ['analysis_notes', 'trading_signals', 'tool_results', 'entry_levels', 'take_profit']:
                    if value and isinstance(value, str):
                        try:
                            parsed = json.loads(value)
                            print(f"    Parsed {col}:")
                            if isinstance(parsed, dict):
                                for k, v in parsed.items():
                                    print(f"      {k}: {type(v).__name__} ({str(v)[:100]}...)" if len(str(v)) > 100 else f"      {k}: {v}")
                            elif isinstance(parsed, list):
                                print(f"      List with {len(parsed)} items")
                                for j, item in enumerate(parsed[:3]):  # Show first 3 items
                                    print(f"        [{j}]: {type(item).__name__}")
                        except json.JSONDecodeError:
                            print(f"    ❌ Invalid JSON in {col}")
                            
        else:
            print("❌ No analysis results found")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")

if __name__ == "__main__":
    check_latest_analysis()
