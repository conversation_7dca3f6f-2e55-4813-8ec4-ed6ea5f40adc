#!/usr/bin/env python3
"""
Comprehensive 500 Error Fix Test with Real Trading Images
Tests the complete trading analysis flow to identify and fix 500 errors.
"""

import asyncio
import sys
import os
import traceback
import base64
import json
from pathlib import Path

# Add backend to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def comprehensive_500_fix_test():
    """Test the complete trading analysis flow with real images."""
    print("🔧 COMPREHENSIVE 500 ERROR FIX TEST")
    print("=" * 60)
    
    try:
        # Load real trading images
        print("\n1. Loading Real Trading Images...")
        testing_images_path = Path(__file__).parent.parent / "Testing_images"
        test_images = []
        
        for image_file in ["Screenshot 2025-08-03 190956.png", "Screenshot 2025-08-03 191041.png"]:
            image_path = testing_images_path / image_file
            if image_path.exists():
                try:
                    with open(image_path, "rb") as f:
                        image_data = base64.b64encode(f.read()).decode()
                        test_images.append(image_data)
                        print(f"   ✅ Loaded: {image_file} ({len(image_data)} chars)")
                except Exception as e:
                    print(f"   ❌ Failed to load {image_file}: {e}")
        
        if not test_images:
            print("   ⚠️  No images found, using minimal test image")
            test_images = ["iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="]
        
        # Test 2: Database and Services
        print("\n2. Testing Database and Services...")
        from core.database import get_db
        from services.trading_service import TradingAnalysisService
        from services.rag_service import FinancialRAGService
        
        async for db in get_db():
            try:
                # Initialize services with proper DB
                trading_service = TradingAnalysisService(db)
                rag_service = FinancialRAGService(db)
                print("   ✅ Services initialized successfully")
                
                # Test RAG metadata generation with complete analysis object
                print("\n3. Testing RAG Metadata Generation...")
                
                # Create complete mock analysis matching real structure
                class CompleteAnalysis:
                    def __init__(self):
                        from uuid import uuid4
                        from datetime import datetime
                        self.id = uuid4()
                        self.user_id = uuid4()
                        self.analysis_type = 'comprehensive'
                        self.market_specialization = 'crypto'
                        self.detected_symbol = 'BTCUSD'
                        self.market_type = 'crypto'
                        self.created_at = datetime.now()
                        self.model_used = 'gemini-2.5-flash'
                        self.execution_time = 84.897455
                        self.confidence_score = 0.0
                        self.timeframes = ['5m', '15m']
                        self.trading_signals = {"status": "complete"}
                        self.analysis_data = {"detected_symbol": "BTCUSD"}
                        self.tool_summaries = {}  # Required attribute that was missing
                
                test_metadata = {
                    'user_query': 'Scalp trading analysis for Crypto market focusing on 5m timeframes',
                    'execution_context': {
                        'preferred_model': 'gemini-2.5-flash',
                        'user_tier': 'free',
                        'image_count': len(test_images)
                    }
                }
                
                mock_analysis = CompleteAnalysis()
                metadata = rag_service._generate_financial_metadata(mock_analysis, test_metadata)
                
                # Validate metadata types
                invalid_types = []
                for key, value in metadata.items():
                    value_type = type(value).__name__
                    if value_type not in ['str', 'int', 'float', 'bool']:
                        invalid_types.append(f"{key}: {value_type}")
                
                if invalid_types:
                    print("   ❌ Invalid metadata types found:")
                    for invalid_type in invalid_types:
                        print(f"      - {invalid_type}")
                else:
                    print("   ✅ All RAG metadata types are ChromaDB compatible!")
                
                break
            except Exception as e:
                print(f"   ❌ Service test failed: {e}")
                traceback.print_exc()
                break
        
        # Test 4: Complete Analysis Request Structure
        print("\n4. Testing Complete Analysis Request...")
        
        # Create the exact request structure that the frontend sends
        analysis_request = {
            "images_base64": test_images,
            "analysis_type": "comprehensive",
            "market_specialization": "crypto",
            "preferred_model": "gemini-2.5-flash"
        }
        
        print(f"   ✅ Request structure created:")
        print(f"      - Images: {len(analysis_request['images_base64'])}")
        print(f"      - Analysis type: {analysis_request['analysis_type']}")
        print(f"      - Market: {analysis_request['market_specialization']}")
        print(f"      - Model: {analysis_request['preferred_model']}")
        
        # Test JSON serialization of request
        try:
            request_json = json.dumps(analysis_request)
            print(f"   ✅ Request serialization successful ({len(request_json)} chars)")
        except Exception as e:
            print(f"   ❌ Request serialization failed: {e}")
        
        # Test 5: Mock Analysis Response Structure
        print("\n5. Testing Analysis Response Structure...")
        
        # Create a response structure that matches what the backend returns
        mock_response = {
            "success": True,
            "data": {
                "success": True,
                "detected_symbol": "BTCUSD",
                "market_type": "crypto",
                "trading_signals": {
                    "status": "Analysis Complete",
                    "analysis_summary": "BTCUSD analysis shows bullish momentum...",
                    "trade_ideas": [
                        {
                            "Direction": "Long",
                            "Entry_Price_Range": "111,000-111,050",
                            "Stop_Loss": "110,750",
                            "Take_Profit_1": "111,250",
                            "Take_Profit_2": "111,450",
                            "Risk_Reward_Ratio": "1.8",
                            "Timeframe": "5M",
                            "Confidence": "7"
                        }
                    ]
                },
                "analysis_data": {
                    "market_context": {"current_price": 111000},
                    "tool_data": {
                        "get_comprehensive_market_news": {"success": True},
                        "get_economic_calendar_risk": {"success": True}
                    }
                }
            },
            "analysis_id": "test-analysis-123",
            "execution_time": 89.12,
            "cached": False,
            "model_used": "gemini-2.5-flash",
            "user_context": {
                "tier": "free",
                "analysis_type": "comprehensive",
                "market_specialization": "crypto"
            }
        }
        
        # Test response serialization
        try:
            response_json = json.dumps(mock_response, default=str)
            print(f"   ✅ Response serialization successful ({len(response_json)} chars)")
            
            # Test deserialization to ensure no circular references
            parsed_response = json.loads(response_json)
            print("   ✅ Response deserialization successful")
            
        except Exception as e:
            print(f"   ❌ Response serialization failed: {e}")
            traceback.print_exc()
        
        # Test 6: Error Scenarios
        print("\n6. Testing Error Scenarios...")
        
        # Test malformed request
        malformed_requests = [
            {"images_base64": []},  # Empty images
            {"images_base64": ["invalid_base64"]},  # Invalid base64
            {},  # Empty request
        ]
        
        for i, req in enumerate(malformed_requests):
            try:
                json.dumps(req)
                print(f"   ✅ Malformed request {i+1} serializable")
            except Exception as e:
                print(f"   ❌ Malformed request {i+1} failed: {e}")
        
        # Summary
        print("\n" + "=" * 60)
        print("🎯 DIAGNOSTIC SUMMARY")
        print("=" * 60)
        
        print("\n✅ WORKING COMPONENTS:")
        print("   - Real trading image loading")
        print("   - Database and service initialization")
        print("   - RAG metadata generation (fixed)")
        print("   - Request/response serialization")
        print("   - JSON handling")
        
        print("\n🔍 POTENTIAL 500 ERROR CAUSES:")
        print("   1. FRONTEND TIMEOUT: Long analysis (89s) might timeout")
        print("   2. PROGRESS TRACKING: Multiple concurrent API calls")
        print("   3. AUTHENTICATION: Token expiry during long requests")
        print("   4. MEMORY ISSUES: Large images + AI processing")
        print("   5. MIDDLEWARE: Response size limits or processing")
        
        print("\n💡 SPECIFIC RECOMMENDATIONS:")
        print("   1. Add request timeout handling in frontend (120s+)")
        print("   2. Implement progress polling instead of long requests")
        print("   3. Add response compression for large payloads")
        print("   4. Check nginx/reverse proxy timeout settings")
        print("   5. Validate authentication token refresh")
        
        print("\n🧪 NEXT STEPS:")
        print("   1. Test with actual API server running")
        print("   2. Monitor network tab during frontend request")
        print("   3. Check server logs for exact error location")
        print("   4. Test with smaller images first")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(comprehensive_500_fix_test())
    if success:
        print("\n🎉 COMPREHENSIVE TEST COMPLETED!")
        print("Ready for real-world testing with API server.")
    else:
        print("\n🚨 TEST FAILED - Additional fixes needed!")
