#!/usr/bin/env python3
"""Quick test for RAG metadata fix"""

import asyncio
import sys
import os
from uuid import uuid4
from datetime import datetime

# Add backend to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_rag_metadata():
    """Test RAG metadata handling for nested dictionaries."""
    print("🧪 Testing RAG metadata fix...")
    
    try:
        from services.rag_service import FinancialRAGService
        rag = FinancialRAGService()
        print("✅ RAG service created successfully")
        
        # Test metadata with nested dictionaries (like the error we saw)
        test_metadata = {
            'user_query': 'Scalp trading analysis for Crypto market focusing on 5m timeframes',
            'execution_context': {
                'preferred_model': 'gemini-2.5-flash',
                'user_tier': 'free',
                'image_count': 1
            }
        }
        
        # Create a mock analysis object
        class MockAnalysis:
            def __init__(self):
                self.id = uuid4()
                self.user_id = uuid4()
                self.analysis_type = 'Scalp'
                self.market_specialization = 'Crypto'
                self.detected_symbol = 'BTCUSD'
                self.market_type = 'crypto'
                self.created_at = datetime.now()
                self.model_used = 'gemini-2.5-flash'
                self.execution_time = 84.897455
                self.confidence_score = 0.0
                self.timeframes = ['5m']
                self.trading_signals = {}
                self.analysis_data = {}
        
        mock_analysis = MockAnalysis()
        metadata = rag._generate_financial_metadata(mock_analysis, test_metadata)
        
        print("✅ Metadata generated successfully!")
        print("🔍 Checking metadata types:")
        
        has_invalid_types = False
        for key, value in metadata.items():
            value_type = type(value).__name__
            is_valid = value_type in ['str', 'int', 'float', 'bool']
            status = "✅" if is_valid else "❌"
            
            if not is_valid:
                has_invalid_types = True
                
            print(f"  {status} {key}: {value_type} = {str(value)[:80]}...")
        
        if has_invalid_types:
            print("❌ Found invalid metadata types - ChromaDB will reject this")
            return False
        else:
            print("✅ All metadata types are ChromaDB compatible!")
            return True
            
    except Exception as e:
        print(f"❌ Error testing RAG metadata: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_rag_metadata())
    if success:
        print("\n🎉 RAG metadata fix is working correctly!")
    else:
        print("\n🚨 RAG metadata fix needs more work!")
