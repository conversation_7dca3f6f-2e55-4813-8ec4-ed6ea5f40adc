/**
 * Centralized Error Handling System
 * Provides beautiful, user-friendly error messages and handling
 */

import toast from 'react-hot-toast'

export interface ErrorDetails {
  message: string
  code?: string
  details?: any
  correlation_id?: string
  timestamp?: string
}

export interface APIError {
  error_type: string
  message: string
  status_code: number
  details?: any
  correlation_id?: string
  timestamp?: string
}

export class TradingError extends Error {
  public code?: string
  public details?: any
  public correlation_id?: string
  public timestamp?: string

  constructor(message: string, code?: string, details?: any, correlation_id?: string) {
    super(message)
    this.name = 'TradingError'
    this.code = code
    this.details = details
    this.correlation_id = correlation_id
    this.timestamp = new Date().toISOString()
  }
}

/**
 * Error message mappings for user-friendly display
 */
const ERROR_MESSAGES: Record<string, string> = {
  // Configuration Errors
  'CONFIG_NOT_FOUND': '⚙️ Configuration file not found. Please check your setup.',
  'GOOGLE_API_KEY_MISSING': '🔑 Google API key is missing. Please configure your API credentials.',
  'DHAN_API_CREDENTIALS_MISSING': '🔑 Dhan API credentials not found. Please configure your trading credentials.',
  
  // Network Errors
  'NETWORK_ERROR': '🌐 Network connection failed. Please check your internet connection.',
  'TIMEOUT_ERROR': '⏱️ Request timed out. Please try again.',
  'CONNECTION_REFUSED': '🚫 Unable to connect to server. Please check if the backend is running.',
  
  // Authentication Errors
  'AUTHENTICATION_ERROR': '🔐 Authentication failed. Please log in again.',
  'AUTHORIZATION_ERROR': '🚫 You don\'t have permission to perform this action.',
  'TOKEN_EXPIRED': '⏰ Your session has expired. Please log in again.',
  
  // API Errors
  'RATE_LIMIT_ERROR': '⚡ Too many requests. Please wait a moment before trying again.',
  'EXTERNAL_API_ERROR': '🔌 External service is temporarily unavailable. Please try again later.',
  'ANALYSIS_ERROR': '📊 Analysis failed. Please check your chart and try again.',
  'RAG_SYSTEM_ERROR': '🧠 Memory system error. Analysis will continue with limited context.',
  
  // File Upload Errors
  'FILE_TOO_LARGE': '📁 File is too large. Please use a smaller image.',
  'INVALID_FILE_TYPE': '📷 Invalid file type. Please upload a valid image (JPG, PNG, WebP).',
  'UPLOAD_FAILED': '📤 Upload failed. Please try again.',
  
  // Trading Errors
  'SYMBOL_NOT_DETECTED': '🔍 Could not detect trading symbol from chart. Please try a clearer image.',
  'MARKET_DATA_ERROR': '📈 Market data unavailable. Analysis will continue with limited data.',
  'TOOL_EXECUTION_ERROR': '🛠️ Tool execution failed. Some analysis features may be limited.',
  
  // Generic Errors
  'UNKNOWN_ERROR': '❓ An unexpected error occurred. Please try again.',
  'VALIDATION_ERROR': '✅ Please check your input and try again.',
  'SERVER_ERROR': '🖥️ Server error. Our team has been notified.',
}

/**
 * Get user-friendly error message
 */
export function getUserFriendlyMessage(error: any): string {
  // Handle API error responses
  if (error?.response?.data) {
    const apiError = error.response.data as APIError
    
    // Check for specific error types
    if (apiError.error_type && ERROR_MESSAGES[apiError.error_type]) {
      return ERROR_MESSAGES[apiError.error_type]
    }
    
    // Use the API message if available
    if (apiError.message) {
      return `❌ ${apiError.message}`
    }
  }
  
  // Handle network errors
  if (error?.code === 'ECONNREFUSED' || error?.message?.includes('ECONNREFUSED')) {
    return ERROR_MESSAGES.CONNECTION_REFUSED
  }
  
  if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
    return ERROR_MESSAGES.NETWORK_ERROR
  }
  
  if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    return ERROR_MESSAGES.TIMEOUT_ERROR
  }
  
  // Handle HTTP status codes
  if (error?.response?.status) {
    switch (error.response.status) {
      case 401:
        return ERROR_MESSAGES.AUTHENTICATION_ERROR
      case 403:
        return ERROR_MESSAGES.AUTHORIZATION_ERROR
      case 429:
        return ERROR_MESSAGES.RATE_LIMIT_ERROR
      case 500:
      case 502:
      case 503:
      case 504:
        return ERROR_MESSAGES.SERVER_ERROR
      default:
        break
    }
  }
  
  // Handle string errors
  if (typeof error === 'string') {
    // Check for specific error patterns
    if (error.includes('config') && error.includes('not found')) {
      return ERROR_MESSAGES.CONFIG_NOT_FOUND
    }
    if (error.includes('API key')) {
      return ERROR_MESSAGES.GOOGLE_API_KEY_MISSING
    }
    if (error.includes('Dhan API')) {
      return ERROR_MESSAGES.DHAN_API_CREDENTIALS_MISSING
    }
    
    return `❌ ${error}`
  }
  
  // Handle Error objects
  if (error instanceof Error) {
    return `❌ ${error.message}`
  }
  
  // Fallback
  return ERROR_MESSAGES.UNKNOWN_ERROR
}

/**
 * Show error toast with beautiful styling
 */
export function showErrorToast(error: any, title?: string) {
  const message = getUserFriendlyMessage(error)
  
  toast.error(message, {
    duration: 6000,
    style: {
      background: 'linear-gradient(135deg, #ff6b6b, #ee5a52)',
      color: 'white',
      fontWeight: '500',
      borderRadius: '12px',
      boxShadow: '0 8px 32px rgba(255, 107, 107, 0.3)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
    },
    iconTheme: {
      primary: 'white',
      secondary: '#ff6b6b',
    },
  })
}

/**
 * Show success toast with beautiful styling
 */
export function showSuccessToast(message: string) {
  toast.success(message, {
    duration: 4000,
    style: {
      background: 'linear-gradient(135deg, #51cf66, #40c057)',
      color: 'white',
      fontWeight: '500',
      borderRadius: '12px',
      boxShadow: '0 8px 32px rgba(81, 207, 102, 0.3)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
    },
    iconTheme: {
      primary: 'white',
      secondary: '#51cf66',
    },
  })
}

/**
 * Show info toast with beautiful styling
 */
export function showInfoToast(message: string) {
  toast(message, {
    duration: 4000,
    style: {
      background: 'linear-gradient(135deg, #339af0, #228be6)',
      color: 'white',
      fontWeight: '500',
      borderRadius: '12px',
      boxShadow: '0 8px 32px rgba(51, 154, 240, 0.3)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
    },
    icon: 'ℹ️',
  })
}

/**
 * Show warning toast with beautiful styling
 */
export function showWarningToast(message: string) {
  toast(message, {
    duration: 5000,
    style: {
      background: 'linear-gradient(135deg, #ffd43b, #fab005)',
      color: '#333',
      fontWeight: '500',
      borderRadius: '12px',
      boxShadow: '0 8px 32px rgba(255, 212, 59, 0.3)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(10px)',
    },
    icon: '⚠️',
  })
}

/**
 * Handle API errors with proper logging and user feedback
 */
export function handleAPIError(error: any, context?: string) {
  // Log error for debugging
  console.error(`API Error${context ? ` in ${context}` : ''}:`, error)
  
  // Extract correlation ID for support
  const correlationId = error?.response?.data?.correlation_id || 
                       error?.correlation_id || 
                       'unknown'
  
  // Log correlation ID for support
  if (correlationId !== 'unknown') {
    console.error(`Correlation ID: ${correlationId}`)
  }
  
  // Show user-friendly error
  showErrorToast(error)
  
  // Return structured error for component handling
  return {
    message: getUserFriendlyMessage(error),
    correlationId,
    originalError: error
  }
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      
      if (attempt === maxRetries) {
        break
      }
      
      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError
}
