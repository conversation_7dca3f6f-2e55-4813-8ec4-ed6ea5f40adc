#!/usr/bin/env python3
"""
Debug script to test trading analysis functionality
"""

import asyncio
import sys
import os
import traceback
from typing import List

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_trading_analysis():
    """Test the trading analysis workflow step by step"""
    
    print("🔍 Testing Trading Analysis Components...")
    
    try:
        # Test 1: Import core dependencies
        print("\n1. Testing core imports...")
        from core.config import settings
        print(f"   ✅ Settings loaded - Google API Key: {'✓' if settings.google_api_key else '✗'}")
        
        # Test 2: Import trading service
        print("\n2. Testing trading service import...")
        from services.trading_service import TradingAnalysisService
        print("   ✅ TradingAnalysisService imported successfully")
        
        # Test 3: Import enhanced LangGraph service
        print("\n3. Testing enhanced LangGraph service...")
        from services.enhanced_langgraph_service import EnhancedLangGraphService
        print("   ✅ EnhancedLangGraphService imported successfully")
        
        # Test 4: Import complete LangGraph workflow
        print("\n4. Testing complete LangGraph workflow...")
        from helpers.complete_langgraph_workflow import CompleteLangGraphTradingWorkflow
        print("   ✅ CompleteLangGraphTradingWorkflow imported successfully")
        
        # Test 5: Test database connection
        print("\n5. Testing database connection...")
        from core.database import get_db
        from models.user import User
        print("   ✅ Database imports successful")
        
        # Test 6: Create mock user for testing
        print("\n6. Creating test user in database...")
        
        # First try to create a real user in the database for testing
        try:
            # Try to get real database session - fix async generator issue
            from core.database import AsyncSessionLocal
            
            async def create_test_user():
                async with AsyncSessionLocal() as session:
                    # Check if test user already exists
                    from sqlalchemy import select
                    
                    # Create a test user if it doesn't exist
                    test_user_email = "<EMAIL>"
                    
                    # Check if user exists
                    stmt = select(User).where(User.email == test_user_email)
                    result = await session.execute(stmt)
                    existing_user = result.scalar_one_or_none()
                    
                    if existing_user:
                        print(f"   ✅ Using existing test user: {existing_user.id}")
                        return existing_user, session
                    else:
                        # Create new test user
                        import uuid
                        from passlib.context import CryptContext
                        
                        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
                        
                        new_user = User(
                            id=str(uuid.uuid4()),
                            email=test_user_email,
                            hashed_password=pwd_context.hash("testpassword"),
                            full_name="Test User",
                            roles=["user"],
                            tier="free",
                            is_active=True,
                            is_verified=True,
                            api_calls_count=0,
                            api_calls_limit=100
                        )
                        
                        session.add(new_user)
                        await session.commit()
                        print(f"   ✅ Created new test user: {new_user.id}")
                        return new_user, session
            
            # Create user and get session
            mock_user, db_session = await create_test_user()
            
        except Exception as e:
            print(f"   ⚠️ Database user creation failed ({e}), using MockDB and MockUser")
            class MockDB:
                def add(self, obj): pass
                async def commit(self): pass
                def rollback(self): pass
                def close(self): pass
            
            class MockUser:
                def __init__(self):
                    self.id = "test-user-id"
                    self.email = "<EMAIL>"
                    self.tier = "free"
                    self.api_calls_count = 0
                    self.api_calls_limit = 100
            
            db_session = MockDB()
            mock_user = MockUser()
            print("   ✅ Mock user and DB created as fallback")
        
        # Test 7: Initialize trading service with real database
        print("\n7. Testing trading service initialization...")
        
        trading_service = TradingAnalysisService(
            db=db_session,
            google_api_key=settings.google_api_key
        )
        print("   ✅ Trading service initialized")
        
        # Test 8: Test with REAL chart data
        print("\n8. Testing with REAL chart analysis...")

        # Use a real chart image from testing_images folder
        chart_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'testing_images', 'Screenshot 2025-08-03 190956.png')

        if not os.path.exists(chart_path):
            print(f"   ❌ Chart image not found at: {chart_path}")
            print("   🔍 Available files in testing_images:")
            testing_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'testing_images')
            if os.path.exists(testing_dir):
                for f in os.listdir(testing_dir):
                    print(f"      - {f}")
            return

        # Load the real chart image
        with open(chart_path, 'rb') as f:
            test_image_bytes = f.read()

        print(f"   ✅ Using real chart image: {os.path.basename(chart_path)}")
        print(f"   📊 Image size: {len(test_image_bytes)} bytes")
        
        try:
            # This should fail gracefully and show us where the issue is
            result = await trading_service.perform_analysis(
                user=mock_user,
                chart_images=[test_image_bytes],
                analysis_type="comprehensive",
                market_specialization="crypto",
                user_query="Test analysis",
                preferred_model="gemini-2.5-flash"
            )
            print(f"   ✅ Analysis completed: {result.get('success', False)}")
            if not result.get('success'):
                print(f"   ⚠️  Analysis failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"   ❌ Analysis failed with exception: {e}")
            print(f"   🔍 Exception type: {type(e).__name__}")
            print(f"   🔍 Traceback: {traceback.format_exc()}")
        
        print("\n✅ Debug test completed!")
        
    except Exception as e:
        print(f"\n❌ Debug test failed: {e}")
        print(f"🔍 Exception type: {type(e).__name__}")
        print(f"🔍 Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_trading_analysis())
