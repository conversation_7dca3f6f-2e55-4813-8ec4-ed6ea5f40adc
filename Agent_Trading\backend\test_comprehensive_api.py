#!/usr/bin/env python3
"""
Comprehensive API Unit Test Suite for AI Trading Analysis API
Tests all endpoints to prevent runtime errors and ensure reliability.
"""

import asyncio
import pytest
import httpx
import json
import os
import sys
from typing import Dict, Any, Optional
from datetime import datetime
import base64

# Add backend to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class APITestSuite:
    """Comprehensive test suite for all API endpoints."""
    
    def __init__(self, base_url: str = "http://localhost:8000", auth_token: str = None):
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.test_results = {}
        
    def get_headers(self, include_auth: bool = True) -> Dict[str, str]:
        """Get request headers with optional authentication."""
        headers = {"Content-Type": "application/json"}
        if include_auth and self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    async def test_health_endpoints(self) -> Dict[str, Any]:
        """Test health and basic endpoints."""
        results = {}
        
        async with httpx.AsyncClient() as client:
            # Test health endpoint
            try:
                response = await client.get(f"{self.base_url}/health")
                results["health"] = {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response": response.json() if response.status_code == 200 else response.text
                }
            except Exception as e:
                results["health"] = {"success": False, "error": str(e)}
            
            # Test docs endpoint
            try:
                response = await client.get(f"{self.base_url}/docs")
                results["docs"] = {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "content_type": response.headers.get("content-type", "")
                }
            except Exception as e:
                results["docs"] = {"success": False, "error": str(e)}
                
        return results
    
    async def test_auth_endpoints(self) -> Dict[str, Any]:
        """Test authentication endpoints."""
        results = {}
        
        async with httpx.AsyncClient() as client:
            # Test registration endpoint
            test_user_data = {
                "email": f"test_{datetime.now().timestamp()}@example.com",
                "password": "testpassword123",
                "full_name": "Test User"
            }
            
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/auth/register",
                    headers=self.get_headers(include_auth=False),
                    json=test_user_data
                )
                results["register"] = {
                    "status_code": response.status_code,
                    "success": response.status_code in [200, 201, 409],  # 409 = user exists
                    "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                }
            except Exception as e:
                results["register"] = {"success": False, "error": str(e)}
            
            # Test login endpoint
            login_data = {
                "username": test_user_data["email"],
                "password": test_user_data["password"]
            }
            
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/auth/login",
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                    data=login_data
                )
                results["login"] = {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                }
                
                # Extract token for subsequent tests
                if response.status_code == 200:
                    response_data = response.json()
                    if "access_token" in response_data:
                        self.auth_token = response_data["access_token"]
                        
            except Exception as e:
                results["login"] = {"success": False, "error": str(e)}
                
        return results
    
    async def test_market_data_endpoints(self) -> Dict[str, Any]:
        """Test market data endpoints."""
        results = {}
        
        async with httpx.AsyncClient() as client:
            endpoints = [
                "/api/v1/market/dashboard-metrics",
                "/api/v1/market/recent-trades",
                "/api/v1/market/ai-insights"
            ]
            
            for endpoint in endpoints:
                try:
                    response = await client.get(
                        f"{self.base_url}{endpoint}",
                        headers=self.get_headers()
                    )
                    endpoint_name = endpoint.split("/")[-1]
                    results[endpoint_name] = {
                        "status_code": response.status_code,
                        "success": response.status_code in [200, 401, 403],  # Auth errors are expected without token
                        "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text[:200]
                    }
                except Exception as e:
                    endpoint_name = endpoint.split("/")[-1]
                    results[endpoint_name] = {"success": False, "error": str(e)}
                    
        return results
    
    async def test_trading_endpoints(self) -> Dict[str, Any]:
        """Test trading analysis endpoints."""
        results = {}
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            # Test trading history endpoint
            try:
                response = await client.get(
                    f"{self.base_url}/api/v1/trading/history",
                    headers=self.get_headers()
                )
                results["history"] = {
                    "status_code": response.status_code,
                    "success": response.status_code in [200, 401, 403],
                    "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text[:200]
                }
            except Exception as e:
                results["history"] = {"success": False, "error": str(e)}
            
            # Test trading analysis endpoint with real trading chart images
            try:
                # Load real trading chart images from Testing_images folder
                test_images = []
                testing_images_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "Testing_images")
                
                for image_file in ["Screenshot 2025-08-03 190956.png", "Screenshot 2025-08-03 191041.png"]:
                    image_path = os.path.join(testing_images_path, image_file)
                    if os.path.exists(image_path):
                        try:
                            with open(image_path, "rb") as f:
                                image_data = base64.b64encode(f.read()).decode()
                                test_images.append(image_data)
                                print(f"   📷 Loaded test image: {image_file}")
                        except Exception as e:
                            print(f"   ⚠️  Could not load {image_file}: {e}")
                
                # Fallback to minimal test image if real images not available
                if not test_images:
                    print("   ⚠️  Using fallback test image")
                    test_images = ["iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="]
                
                analysis_request = {
                    "images_base64": test_images,
                    "analysis_type": "comprehensive", 
                    "market_specialization": "crypto",
                    "preferred_model": "gemini-2.5-flash"
                }
                
                response = await client.post(
                    f"{self.base_url}/api/v1/trading/analyze",
                    headers=self.get_headers(),
                    json=analysis_request
                )
                results["analyze"] = {
                    "status_code": response.status_code,
                    "success": response.status_code in [200, 400, 401, 403, 422, 500],  # Various expected responses
                    "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text[:500]
                }
            except Exception as e:
                results["analyze"] = {"success": False, "error": str(e)}
                
        return results
    
    async def test_progress_endpoints(self) -> Dict[str, Any]:
        """Test progress tracking endpoints."""
        results = {}
        
        async with httpx.AsyncClient() as client:
            # Test progress session creation
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/progress/session",
                    headers=self.get_headers()
                )
                results["create_session"] = {
                    "status_code": response.status_code,
                    "success": response.status_code in [200, 401, 403],
                    "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text[:200]
                }
                
                # Extract session_id for subsequent tests
                session_id = None
                if response.status_code == 200:
                    response_data = response.json()
                    session_id = response_data.get("session_id")
                
            except Exception as e:
                results["create_session"] = {"success": False, "error": str(e)}
                session_id = "test-session-id"  # Fallback for testing
            
            # Test progress session status
            if session_id:
                try:
                    response = await client.get(
                        f"{self.base_url}/api/v1/progress/session/{session_id}",
                        headers=self.get_headers()
                    )
                    results["get_session"] = {
                        "status_code": response.status_code,
                        "success": response.status_code in [200, 404, 401, 403],
                        "response": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text[:200]
                    }
                except Exception as e:
                    results["get_session"] = {"success": False, "error": str(e)}
                    
        return results
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all API tests and return comprehensive results."""
        print("🧪 Starting Comprehensive API Test Suite...")
        
        all_results = {
            "test_start_time": datetime.now().isoformat(),
            "base_url": self.base_url
        }
        
        # Test health endpoints
        print("\n1. Testing Health Endpoints...")
        all_results["health_tests"] = await self.test_health_endpoints()
        
        # Test auth endpoints
        print("2. Testing Authentication Endpoints...")
        all_results["auth_tests"] = await self.test_auth_endpoints()
        
        # Test market data endpoints
        print("3. Testing Market Data Endpoints...")
        all_results["market_tests"] = await self.test_market_data_endpoints()
        
        # Test trading endpoints
        print("4. Testing Trading Endpoints...")
        all_results["trading_tests"] = await self.test_trading_endpoints()
        
        # Test progress endpoints
        print("5. Testing Progress Endpoints...")
        all_results["progress_tests"] = await self.test_progress_endpoints()
        
        all_results["test_end_time"] = datetime.now().isoformat()
        
        return all_results
    
    def print_test_summary(self, results: Dict[str, Any]):
        """Print a formatted summary of test results."""
        print("\n" + "="*60)
        print("🎯 API TEST SUMMARY")
        print("="*60)
        
        total_tests = 0
        passed_tests = 0
        
        for category, tests in results.items():
            if category.endswith("_tests"):
                print(f"\n📋 {category.replace('_tests', '').title()} Tests:")
                for test_name, test_result in tests.items():
                    total_tests += 1
                    status = "✅ PASS" if test_result.get("success", False) else "❌ FAIL"
                    status_code = test_result.get("status_code", "N/A")
                    print(f"   {status} - {test_name} (Status: {status_code})")
                    
                    if test_result.get("success", False):
                        passed_tests += 1
                    elif test_result.get("error"):
                        print(f"      Error: {test_result['error']}")
        
        print(f"\n🎯 Overall Results: {passed_tests}/{total_tests} tests passed")
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        print(f"📊 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 API endpoints are functioning well!")
        elif success_rate >= 60:
            print("⚠️  Some endpoints need attention")
        else:
            print("🚨 Multiple endpoints require fixes")


async def main():
    """Main test execution function."""
    # Test with local development server
    tester = APITestSuite(base_url="http://localhost:8000")
    
    results = await tester.run_all_tests()
    tester.print_test_summary(results)
    
    # Save detailed results to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"api_test_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: {results_file}")


if __name__ == "__main__":
    asyncio.run(main())
