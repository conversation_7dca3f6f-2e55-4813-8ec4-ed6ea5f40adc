"""
Production-ready FastAPI application with enterprise-grade security and monitoring.
Implements JWT authentication, rate limiting, structured logging, and comprehensive error handling.
"""

import time
import uuid
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Response, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

# Import core modules (fix relative imports)
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.config import settings
from core.logging import (
    get_logger, set_correlation_id, generate_correlation_id,
    request_logger, configure_logging
)
from core.redis_client import redis_client
from core.exceptions import (
    TradingAnalysisException, AuthenticationError, AuthorizationError,
    RateLimitError, ExternalAPIError, AnalysisError, RAGSystemError
)

# Import middleware
from api.middleware.error_handler import (
    ErrorHandlingMiddleware, authentication_exception_handler,
    authorization_exception_handler, rate_limit_exception_handler,
    external_api_exception_handler
)
from api.middleware.rate_limiter import RateLimitMiddleware

# Import routes
from api.routes import auth, health, trading, users, market_data, progress, async_trading
# Re-enable RAG routes after fixing compatibility issues
from api.routes import rag


# Configure logging
configure_logging()
logger = get_logger("main")


class CorrelationIDMiddleware(BaseHTTPMiddleware):
    """Middleware to add correlation IDs to all requests."""
    
    async def dispatch(self, request: Request, call_next):
        # Generate or extract correlation ID
        correlation_id = request.headers.get("X-Correlation-ID") or generate_correlation_id()
        set_correlation_id(correlation_id)
        
        # Add to request state for access in route handlers
        request.state.correlation_id = correlation_id
        
        # Process request
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        
        # Add correlation ID to response headers
        response.headers["X-Correlation-ID"] = correlation_id
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for comprehensive request logging."""
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Extract user info if available
        user_id = getattr(request.state, 'user_id', None)
        client_ip = self._get_client_ip(request)
        
        # Log request start
        request_logger.log_request_start(
            method=request.method,
            path=str(request.url.path),
            client_ip=client_ip,
            user_id=user_id
        )
        
        try:
            response = await call_next(request)
            execution_time = time.time() - start_time
            
            # Log successful request
            request_logger.log_request_end(
                method=request.method,
                path=str(request.url.path),
                status_code=response.status_code,
                execution_time=execution_time,
                user_id=user_id
            )
            
            return response
            
        except Exception as e:
            # Log failed request
            request_logger.log_request_error(
                method=request.method,
                path=str(request.url.path),
                error=e,
                user_id=user_id
            )
            raise
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request."""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management with startup and shutdown tasks."""
    
    # Startup tasks
    logger.info(
        "Application starting up",
        version=settings.APP_VERSION,
        environment=settings.ENVIRONMENT,
        debug=settings.DEBUG
    )
    
    try:
        # Initialize services
        logger.info("Initializing database...")
        from core.database import init_database
        init_database()
        logger.info("Database initialized successfully")
        
        # Initialize Redis connection
        logger.info("Initializing Redis connection...")
        await redis_client.connect()
        redis_health = await redis_client.health_check()
        logger.info(f"Redis connected: {redis_health['status']}")

        # Test ChromaDB connection via FinancialRAGService
        logger.info("Testing ChromaDB connection...")
        try:
            from services.rag_service import FinancialRAGService
            from core.database import get_db

            # Test RAG service initialization
            async for db in get_db():
                rag_service = FinancialRAGService(db)
                await rag_service.initialize()
                stats = await rag_service.get_rag_statistics()
                break

        except Exception as e:
            logger.warning(f"ChromaDB initialization failed: {e}")
            logger.info("Continuing without ChromaDB - RAG features will be limited")

        # Test Google API key
        if settings.GOOGLE_API_KEY:
            logger.info("Google API key configured")
        else:
            logger.warning("Google API key not configured")

        logger.info("Application startup completed successfully")
        
    except Exception as e:
        logger.error("Application startup failed", error=e)
        raise
    
    # Application is running
    yield
    
    # Shutdown tasks
    logger.info("Application shutting down")
    
    try:
        # Close Redis connection
        await redis_client.disconnect()
        logger.info("Application shutdown completed successfully")
        
    except Exception as e:
        logger.error("Error during application shutdown", error=e)


def create_application() -> FastAPI:
    """Create and configure FastAPI application."""
    
    # Create FastAPI app
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.APP_VERSION,
        description="Enterprise-grade AI Trading Analysis API with advanced security and monitoring",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        openapi_url="/openapi.json" if settings.DEBUG else None,
        lifespan=lifespan  # Use lifespan instead of on_event
    )
    
    # Security middleware
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"] if settings.DEBUG else ["localhost", "127.0.0.1", "aitrading.com"]
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.BACKEND_CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["X-Correlation-ID", "X-Process-Time"]
    )
    
    # Compression middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Custom middleware (order matters!)
    app.add_middleware(CorrelationIDMiddleware)
    app.add_middleware(RequestLoggingMiddleware)
    # Temporarily disable rate limiting for development debugging
    if not settings.DEBUG:
        app.add_middleware(RateLimitMiddleware)
    app.add_middleware(ErrorHandlingMiddleware)
    
    # Exception handlers
    app.add_exception_handler(AuthenticationError, authentication_exception_handler)
    app.add_exception_handler(AuthorizationError, authorization_exception_handler)
    app.add_exception_handler(RateLimitError, rate_limit_exception_handler)
    app.add_exception_handler(ExternalAPIError, external_api_exception_handler)
    
    # Include routers
    app.include_router(auth.router, prefix=settings.API_V1_PREFIX)
    app.include_router(async_trading.router, prefix=settings.API_V1_PREFIX)  # New async trading (no timeouts)
    app.include_router(trading.router, prefix=settings.API_V1_PREFIX)  # OLD - kept for compatibility with other components
    app.include_router(market_data.router, prefix=settings.API_V1_PREFIX)
    # RAG router re-enabled after fixing compatibility issues
    app.include_router(rag.router, prefix=settings.API_V1_PREFIX)
    app.include_router(users.router, prefix=settings.API_V1_PREFIX)
    app.include_router(progress.router, prefix=settings.API_V1_PREFIX)  # Add progress tracking
    app.include_router(health.router)
    
    # Root endpoint
    @app.get("/")
    async def root():
        """API root endpoint with basic information."""
        return {
            "name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT,
            "status": "operational",
            "docs": "/docs" if settings.DEBUG else "disabled",
            "health": "/health",
            "auth": f"{settings.API_V1_PREFIX}/auth"
        }
    
    return app


# Create application instance
app = create_application()


# Additional security headers
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    """Add security headers to all responses."""
    response = await call_next(request)
    
    # Security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
    
    if not settings.DEBUG:
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    
    return response


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True
    )
